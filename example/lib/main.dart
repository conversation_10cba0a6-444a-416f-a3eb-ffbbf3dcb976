/// ͼ���� FFI ��ʹ��ʾ��
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_blend_ffi/image_blend.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Image Blend FFI Demo',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const ImageBlendDemo(),
    );
  }
}

class ImageBlendDemo extends StatefulWidget {
  const ImageBlendDemo({super.key});

  @override
  State<ImageBlendDemo> createState() => _ImageBlendDemoState();
}

class _ImageBlendDemoState extends State<ImageBlendDemo> {
  String _status = '׼������';
  bool _isProcessing = false;
  String? _outputImagePath;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Blend FFI Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // ״̬��ʾ
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '״̬',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    if (_isProcessing)
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: LinearProgressIndicator(),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // ���ܰ�ť
            const Text(
              '���ܲ���',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            // ͼ����Ӳ���
            ElevatedButton(
              onPressed: _isProcessing ? null : _testOverlayImages,
              child: const Text('����ͼ����� (compute)'),
            ),
            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: _isProcessing ? null : _testOverlayImagesIsolate,
              child: const Text('����ͼ����� (Isolate)'),
            ),
            const SizedBox(height: 8),

            // ͼ��ƽ�̲���
            ElevatedButton(
              onPressed: _isProcessing ? null : _testTileImage,
              child: const Text('����ͼ��ƽ��'),
            ),
            const SizedBox(height: 8),

            // ͼ��ü�����
            ElevatedButton(
              onPressed: _isProcessing ? null : _testTrimImage,
              child: const Text('����ͼ��ü�'),
            ),
            const SizedBox(height: 8),

            // �����������
            ElevatedButton(
              onPressed: _isProcessing ? null : _testClearCache,
              child: const Text('��������'),
            ),
            const SizedBox(height: 16),

            // ���ͼ����ʾ
            if (_outputImagePath != null) ...[
              const Text(
                '������',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.file(
                      File(_outputImagePath!),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(child: Text('�޷���ʾͼ��'));
                      },
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// ����ͼ����� (ʹ�� compute)
  Future<void> _testOverlayImages() async {
    setState(() {
      _isProcessing = true;
      _status = '����ִ��ͼ����� (compute)...';
    });

    try {
      // ��ȡӦ���ĵ�Ŀ¼
      final directory = await getApplicationDocumentsDirectory();
      final outputPath = '${directory.path}/overlay_output.png';

      // ������Ҫ�滻Ϊʵ�ʵ�ͼ��·��
      // ��ʵ��Ӧ���У�����Ҫ����Դ���û�ѡ����ļ��л�ȡ·��
      const baseImagePath = '/path/to/base/image.png';
      const topImagePath = '/path/to/top/image.png';

      final result = await overlayImages(
        baseImagePath: baseImagePath,
        topImagePath: topImagePath,
        outputPath: outputPath,
        blendMode: BlendMode.normal,
        opacity: 75,
      );

      if (result.isSuccess) {
        setState(() {
          _status = 'ͼ�������� (compute)';
          _outputImagePath = outputPath;
        });
      } else {
        setState(() {
          _status = 'ͼ�����ʧ��: ${result.errorMessage}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'ͼ������쳣: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// ����ͼ����� (ʹ�� Isolate)
  Future<void> _testOverlayImagesIsolate() async {
    setState(() {
      _isProcessing = true;
      _status = '����ִ��ͼ����� (Isolate)...';
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final outputPath = '${directory.path}/overlay_isolate_output.png';

      const baseImagePath = '/path/to/base/image.png';
      const topImagePath = '/path/to/top/image.png';

      final result = await overlayImagesIsolate(
        baseImagePath: baseImagePath,
        topImagePath: topImagePath,
        outputPath: outputPath,
        blendMode: BlendMode.multiply,
        opacity: 50,
      );

      if (result.isSuccess) {
        setState(() {
          _status = 'ͼ�������� (Isolate)';
          _outputImagePath = outputPath;
        });
      } else {
        setState(() {
          _status = 'ͼ�����ʧ��: ${result.errorMessage}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'ͼ������쳣: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// ����ͼ��ƽ��
  Future<void> _testTileImage() async {
    setState(() {
      _isProcessing = true;
      _status = '����ִ��ͼ��ƽ��...';
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final outputPath = '${directory.path}/tile_output.png';

      const imagePath = '/path/to/image.png';

      final result = await tileImage(
        imagePath: imagePath,
        outputPath: outputPath,
        tileMultiplier: 3,
      );

      if (result.isSuccess) {
        setState(() {
          _status = 'ͼ��ƽ�����';
          _outputImagePath = outputPath;
        });
      } else {
        setState(() {
          _status = 'ͼ��ƽ��ʧ��: ${result.errorMessage}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'ͼ��ƽ���쳣: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// ����ͼ��ü�
  Future<void> _testTrimImage() async {
    setState(() {
      _isProcessing = true;
      _status = '����ִ��ͼ��ü�...';
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final outputPath = '${directory.path}/trim_output.png';

      const inputPath = '/path/to/input/image.png';

      final result = await trimImage(
        inputPath: inputPath,
        outputPath: outputPath,
      );

      if (result.isSuccess) {
        setState(() {
          _status = 'ͼ��ü����';
          _outputImagePath = outputPath;
        });
      } else {
        setState(() {
          _status = 'ͼ��ü�ʧ��: ${result.errorMessage}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'ͼ��ü��쳣: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// ������������
  Future<void> _testClearCache() async {
    setState(() {
      _isProcessing = true;
      _status = '������������...';
    });

    try {
      final result = await clearCache();

      if (result.isSuccess) {
        setState(() {
          _status = '�����������';
        });
      } else {
        setState(() {
          _status = '��������ʧ��: ${result.errorMessage}';
        });
      }
    } catch (e) {
      setState(() {
        _status = '���������쳣: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
