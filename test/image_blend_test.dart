/// ͼ���� FFI ��ĵ�Ԫ����
import 'package:flutter_test/flutter_test.dart';
import 'package:image_blend_ffi/image_blend.dart';
import 'dart:io';

void main() {
  group('ImageBlend FFI Tests', () {
    late String testDir;
    late String baseImagePath;
    late String topImagePath;
    late String maskImagePath;
    late String outputPath;

    setUpAll(() async {
      // ��������Ŀ¼
      testDir = Directory.systemTemp.createTempSync('image_blend_test').path;
      baseImagePath = '$testDir/base.png';
      topImagePath = '$testDir/top.png';
      maskImagePath = '$testDir/mask.png';
      outputPath = '$testDir/output.png';

      // ��������ͼ���ļ�������ֻ�Ǵ������ļ���ʵ�ʲ�����Ҫ��ʵͼ��
      await File(baseImagePath).create();
      await File(topImagePath).create();
      await File(maskImagePath).create();
    });

    tearDownAll(() async {
      // ��������Ŀ¼
      final dir = Directory(testDir);
      if (await dir.exists()) {
        await dir.delete(recursive: true);
      }
    });

    group('BlendMode Tests', () {
      test('BlendMode values should be correct', () {
        expect(BlendMode.normal.value, equals(0));
        expect(BlendMode.multiply.value, equals(1));
        expect(BlendMode.overlay.value, equals(2));
        expect(BlendMode.softLight.value, equals(3));
        expect(BlendMode.screen.value, equals(4));
      });
    });

    group('TilingMode Tests', () {
      test('TilingMode values should be correct', () {
        expect(TilingMode.stretch.value, equals(0));
        expect(TilingMode.tile.value, equals(1));
      });
    });

    group('FFIResult Tests', () {
      test('FFIResult.success() should create success result', () {
        final result = FFIResult.success();
        expect(result.isSuccess, isTrue);
        expect(result.errorCode, equals(FFIErrorCodes.success));
        expect(result.errorMessage, isNull);
      });

      test('FFIResult.error() should create error result', () {
        final result = FFIResult.error(
          FFIErrorCodes.fileNotFound,
          'Test error',
        );
        expect(result.isSuccess, isFalse);
        expect(result.errorCode, equals(FFIErrorCodes.fileNotFound));
        expect(result.errorMessage, equals('Test error'));
      });

      test('FFIResult.error() should use default message', () {
        final result = FFIResult.error(FFIErrorCodes.invalidParams);
        expect(result.isSuccess, isFalse);
        expect(result.errorCode, equals(FFIErrorCodes.invalidParams));
        expect(result.errorMessage, equals('������Ч'));
      });
    });

    group('OverlayImageParams Tests', () {
      test('OverlayImageParams.isValid() should validate correctly', () {
        // ��Ч����
        final validParams = OverlayImageParams(
          baseImagePath: baseImagePath,
          topImagePath: topImagePath,
          outputPath: outputPath,
          opacity: 50,
          tilingScale: 100,
        );
        expect(validParams.isValid(), isTrue);

        // ��Ч���� - ��·��
        final invalidParams1 = OverlayImageParams(
          baseImagePath: '',
          topImagePath: topImagePath,
          outputPath: outputPath,
        );
        expect(invalidParams1.isValid(), isFalse);

        // ��Ч���� - ͸���ȳ�����Χ
        final invalidParams2 = OverlayImageParams(
          baseImagePath: baseImagePath,
          topImagePath: topImagePath,
          outputPath: outputPath,
          opacity: 150,
        );
        expect(invalidParams2.isValid(), isFalse);

        // ��Ч���� - ���ű���������Χ
        final invalidParams3 = OverlayImageParams(
          baseImagePath: baseImagePath,
          topImagePath: topImagePath,
          outputPath: outputPath,
          tilingScale: 2000,
        );
        expect(invalidParams3.isValid(), isFalse);
      });
    });

    group('TileImageParams Tests', () {
      test('TileImageParams.isValid() should validate correctly', () {
        // ��Ч����
        final validParams = TileImageParams(
          imagePath: baseImagePath,
          outputPath: outputPath,
          tileMultiplier: 3,
        );
        expect(validParams.isValid(), isTrue);

        // ��Ч���� - ��·��
        final invalidParams1 = TileImageParams(
          imagePath: '',
          outputPath: outputPath,
          tileMultiplier: 3,
        );
        expect(invalidParams1.isValid(), isFalse);

        // ��Ч���� - ����������Χ
        final invalidParams2 = TileImageParams(
          imagePath: baseImagePath,
          outputPath: outputPath,
          tileMultiplier: 15,
        );
        expect(invalidParams2.isValid(), isFalse);
      });
    });

    group('TrimImageParams Tests', () {
      test('TrimImageParams.isValid() should validate correctly', () {
        // ��Ч����
        final validParams = TrimImageParams(
          inputPath: baseImagePath,
          outputPath: outputPath,
        );
        expect(validParams.isValid(), isTrue);

        // ��Ч���� - ��·��
        final invalidParams = TrimImageParams(
          inputPath: '',
          outputPath: outputPath,
        );
        expect(invalidParams.isValid(), isFalse);
      });
    });

    group('ImageBlendService Tests', () {
      test('ImageBlendService should be singleton', () {
        final service1 = ImageBlendService();
        final service2 = ImageBlendService();
        expect(identical(service1, service2), isTrue);
      });
    });

    // ע�⣺���²�����Ҫ��ʵ��ͼ���ļ�����ȷ���õ� FFI ��
    // ��ʵ�ʻ���������ʱ�Ż�ͨ��

    group('Integration Tests', () {
      test(
        'overlayImages should handle file not found error',
        () async {
          final result = await overlayImages(
            baseImagePath: '/nonexistent/base.png',
            topImagePath: '/nonexistent/top.png',
            outputPath: '/tmp/output.png',
          );

          expect(result.isSuccess, isFalse);
          expect(result.errorCode, equals(FFIErrorCodes.fileNotFound));
        },
        skip: 'Requires FFI library setup',
      );

      test(
        'tileImage should handle file not found error',
        () async {
          final result = await tileImage(
            imagePath: '/nonexistent/image.png',
            outputPath: '/tmp/output.png',
          );

          expect(result.isSuccess, isFalse);
          expect(result.errorCode, equals(FFIErrorCodes.fileNotFound));
        },
        skip: 'Requires FFI library setup',
      );

      test(
        'trimImage should handle file not found error',
        () async {
          final result = await trimImage(
            inputPath: '/nonexistent/input.png',
            outputPath: '/tmp/output.png',
          );

          expect(result.isSuccess, isFalse);
          expect(result.errorCode, equals(FFIErrorCodes.fileNotFound));
        },
        skip: 'Requires FFI library setup',
      );

      test('clearCache should work', () async {
        final result = await clearCache();

        // ��û�� FFI �������£����ʧ��
        // ������ȷ���õĻ�����Ӧ�óɹ�
        expect(result, isA<FFIResult>());
      }, skip: 'Requires FFI library setup');
    });

    group('Batch Processing Tests', () {
      test('batchOverlayImages should handle empty list', () async {
        final results = await batchOverlayImages([]);
        expect(results, isEmpty);
      });

      test(
        'batchOverlayImages should process multiple items',
        () async {
          final paramsList = [
            OverlayImageParams(
              baseImagePath: '/nonexistent/base1.png',
              topImagePath: '/nonexistent/top1.png',
              outputPath: '/tmp/output1.png',
            ),
            OverlayImageParams(
              baseImagePath: '/nonexistent/base2.png',
              topImagePath: '/nonexistent/top2.png',
              outputPath: '/tmp/output2.png',
            ),
          ];

          final results = await batchOverlayImages(paramsList);
          expect(results.length, equals(2));

          // ���н����Ӧ����ʧ�ܵģ��ļ������ڣ�
          for (final result in results) {
            expect(result.isSuccess, isFalse);
          }
        },
        skip: 'Requires FFI library setup',
      );
    });
  });
}
