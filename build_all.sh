#!/bin/bash

# 全平台构建脚本 - 生成Android .so和iOS .framework文件
# 使用方法: ./build_all.sh [选项]
# 选项: android, ios, all (默认)

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
BUILD_TARGET=${1:-"all"}

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}  Image Blend FFI 全平台构建工具${NC}"
echo -e "${BLUE}======================================${NC}"
echo -e "${YELLOW}构建目标: $BUILD_TARGET${NC}"
echo ""

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_ROOT"

# 检查必要文件
check_files() {
    echo -e "${GREEN}检查必要文件...${NC}"
    
    if [ ! -f "src/image_blend_ffi/image_blend_ffi.cpp" ]; then
        echo -e "${RED}错误: 未找到FFI源文件${NC}"
        exit 1
    fi
    
    if [ ! -f "src/image_blend_ffi/image_blend_ffi.h" ]; then
        echo -e "${RED}错误: 未找到FFI头文件${NC}"
        exit 1
    fi
    
    if [ ! -f "CMakeLists.txt" ]; then
        echo -e "${RED}错误: 未找到CMakeLists.txt${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}? 所有必要文件存在${NC}"
}

# 构建Android
build_android() {
    echo -e "${GREEN}开始构建Android...${NC}"
    
    # 检查环境
    if [ -z "$ANDROID_NDK_ROOT" ] && [ -z "$ANDROID_NDK" ]; then
        echo -e "${YELLOW}警告: 未设置Android NDK环境变量，跳过Android构建${NC}"
        echo "请设置: export ANDROID_NDK_ROOT=/path/to/android-ndk"
        return 1
    fi
    
    if [ -z "$OPENCV_ANDROID_SDK" ]; then
        echo -e "${YELLOW}警告: 未设置OpenCV Android SDK环境变量，跳过Android构建${NC}"
        echo "请设置: export OPENCV_ANDROID_SDK=/path/to/OpenCV-android-sdk"
        return 1
    fi
    
    # 构建不同架构
    local architectures=("arm64-v8a" "armeabi-v7a")
    
    for arch in "${architectures[@]}"; do
        echo -e "${YELLOW}构建Android $arch...${NC}"
        
        if [ -f "android/build_android.sh" ]; then
            chmod +x android/build_android.sh
            ./android/build_android.sh "$arch"
        else
            echo -e "${RED}错误: 未找到android/build_android.sh${NC}"
            return 1
        fi
    done
    
    echo -e "${GREEN}? Android构建完成${NC}"
    
    # 显示结果
    echo -e "${GREEN}Android构建结果:${NC}"
    find android/libs -name "*.so" -type f | while read file; do
        echo -e "  ${BLUE}$file${NC}"
        ls -la "$file"
    done
}

# 构建iOS
build_ios() {
    echo -e "${GREEN}开始构建iOS...${NC}"
    
    # 检查环境
    if ! command -v xcodebuild &> /dev/null; then
        echo -e "${YELLOW}警告: 未找到Xcode，跳过iOS构建${NC}"
        return 1
    fi
    
    if [ -z "$OPENCV_IOS_SDK" ]; then
        echo -e "${YELLOW}警告: 未设置OpenCV iOS SDK环境变量，跳过iOS构建${NC}"
        echo "请设置: export OPENCV_IOS_SDK=/path/to/opencv2.framework"
        return 1
    fi
    
    echo -e "${YELLOW}构建iOS通用framework...${NC}"
    
    if [ -f "ios/build_ios.sh" ]; then
        chmod +x ios/build_ios.sh
        ./ios/build_ios.sh "UNIVERSAL"
    else
        echo -e "${RED}错误: 未找到ios/build_ios.sh${NC}"
        return 1
    fi
    
    echo -e "${GREEN}? iOS构建完成${NC}"
    
    # 显示结果
    echo -e "${GREEN}iOS构建结果:${NC}"
    if [ -d "ios/frameworks/image_blend_ffi.framework" ]; then
        echo -e "  ${BLUE}ios/frameworks/image_blend_ffi.framework${NC}"
        ls -la "ios/frameworks/image_blend_ffi.framework/"
        echo -e "  支持的架构:"
        lipo -info "ios/frameworks/image_blend_ffi.framework/image_blend_ffi"
    fi
}

# 清理函数
clean_builds() {
    echo -e "${YELLOW}清理构建目录...${NC}"
    rm -rf build_android_*
    rm -rf build_ios_*
    rm -rf android/libs
    rm -rf ios/frameworks
    echo -e "${GREEN}? 清理完成${NC}"
}

# 显示使用说明
show_usage() {
    echo -e "${BLUE}使用说明:${NC}"
    echo "  ./build_all.sh [选项]"
    echo ""
    echo -e "${BLUE}选项:${NC}"
    echo "  android  - 只构建Android .so文件"
    echo "  ios      - 只构建iOS .framework文件"
    echo "  all      - 构建所有平台 (默认)"
    echo "  clean    - 清理所有构建文件"
    echo ""
    echo -e "${BLUE}环境变量:${NC}"
    echo "  ANDROID_NDK_ROOT     - Android NDK路径"
    echo "  OPENCV_ANDROID_SDK   - OpenCV Android SDK路径"
    echo "  OPENCV_IOS_SDK       - OpenCV iOS SDK路径"
    echo ""
    echo -e "${BLUE}示例:${NC}"
    echo "  export ANDROID_NDK_ROOT=/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653"
    echo "  export OPENCV_ANDROID_SDK=/path/to/OpenCV-android-sdk"
    echo "  export OPENCV_IOS_SDK=/path/to/opencv2.framework"
    echo "  ./build_all.sh all"
}

# 主函数
main() {
    case $BUILD_TARGET in
        "android")
            check_files
            build_android
            ;;
        "ios")
            check_files
            build_ios
            ;;
        "all")
            check_files
            build_android || echo -e "${YELLOW}Android构建跳过${NC}"
            build_ios || echo -e "${YELLOW}iOS构建跳过${NC}"
            ;;
        "clean")
            clean_builds
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            echo -e "${RED}错误: 未知选项 '$BUILD_TARGET'${NC}"
            show_usage
            exit 1
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}======================================${NC}"
    echo -e "${GREEN}  构建完成!${NC}"
    echo -e "${GREEN}======================================${NC}"
}

# 运行主函数
main
