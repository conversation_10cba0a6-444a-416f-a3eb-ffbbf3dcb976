#!/bin/bash

# Android构建脚本 - 生成.so文件并静态链接OpenCV
# 使用方法: ./build_android.sh [架构]
# 支持的架构: arm64-v8a, armeabi-v7a, x86, x86_64

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_ABI="arm64-v8a"
DEFAULT_API_LEVEL="21"
DEFAULT_NDK_VERSION="25.2.9519653"

# 检查参数
ABI=${1:-$DEFAULT_ABI}
API_LEVEL=${2:-$DEFAULT_API_LEVEL}

echo -e "${GREEN}开始构建Android .so文件${NC}"
echo -e "${YELLOW}目标架构: $ABI${NC}"
echo -e "${YELLOW}API级别: $API_LEVEL${NC}"

# 检查环境变量
if [ -z "$ANDROID_NDK_ROOT" ] && [ -z "$ANDROID_NDK" ]; then
    echo -e "${RED}错误: 请设置ANDROID_NDK_ROOT或ANDROID_NDK环境变量${NC}"
    echo "例如: export ANDROID_NDK_ROOT=/path/to/android-ndk"
    exit 1
fi

# 设置NDK路径
NDK_PATH=${ANDROID_NDK_ROOT:-$ANDROID_NDK}
if [ ! -d "$NDK_PATH" ]; then
    echo -e "${RED}错误: NDK路径不存在: $NDK_PATH${NC}"
    exit 1
fi

# 检查OpenCV
if [ -z "$OPENCV_ANDROID_SDK" ]; then
    echo -e "${RED}错误: 请设置OPENCV_ANDROID_SDK环境变量${NC}"
    echo "例如: export OPENCV_ANDROID_SDK=/path/to/OpenCV-android-sdk"
    exit 1
fi

if [ ! -d "$OPENCV_ANDROID_SDK" ]; then
    echo -e "${RED}错误: OpenCV Android SDK路径不存在: $OPENCV_ANDROID_SDK${NC}"
    exit 1
fi

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build_android_$ABI"
OUTPUT_DIR="$PROJECT_ROOT/android/libs/$ABI"

echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${YELLOW}构建目录: $BUILD_DIR${NC}"
echo -e "${YELLOW}输出目录: $OUTPUT_DIR${NC}"

# 清理并创建构建目录
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
mkdir -p "$OUTPUT_DIR"

# 进入构建目录
cd "$BUILD_DIR"

echo -e "${GREEN}配置CMake...${NC}"

# 配置CMake
cmake \
    -DCMAKE_TOOLCHAIN_FILE="$NDK_PATH/build/cmake/android.toolchain.cmake" \
    -DANDROID_ABI="$ABI" \
    -DANDROID_PLATFORM="android-$API_LEVEL" \
    -DANDROID_NDK="$NDK_PATH" \
    -DCMAKE_BUILD_TYPE=Release \
    -DANDROID_STL=c++_shared \
    -DANDROID_CPP_FEATURES="rtti exceptions" \
    -DOPENCV_ANDROID_SDK="$OPENCV_ANDROID_SDK" \
    -DSTATIC_OPENCV=ON \
    -DCMAKE_FIND_ROOT_PATH_MODE_PACKAGE=BOTH \
    -DOpenCV_DIR="$OPENCV_ANDROID_SDK/sdk/native/jni" \
    -DOpenCV_STATIC=ON \
    "$PROJECT_ROOT"

echo -e "${GREEN}开始编译...${NC}"

# 编译
cmake --build . --config Release -j$(nproc)

# 检查生成的文件
if [ -f "lib/libimage_blend_ffi.so" ]; then
    echo -e "${GREEN}编译成功!${NC}"
    
    # 复制到输出目录
    cp "lib/libimage_blend_ffi.so" "$OUTPUT_DIR/"
    
    # 显示文件信息
    echo -e "${GREEN}生成的文件信息:${NC}"
    ls -la "$OUTPUT_DIR/libimage_blend_ffi.so"
    file "$OUTPUT_DIR/libimage_blend_ffi.so"
    
    # 检查依赖
    echo -e "${GREEN}依赖检查:${NC}"
    "$NDK_PATH/toolchains/llvm/prebuilt/*/bin/llvm-readelf" -d "$OUTPUT_DIR/libimage_blend_ffi.so" | grep NEEDED || true
    
else
    echo -e "${RED}编译失败: 未找到libimage_blend_ffi.so${NC}"
    exit 1
fi

echo -e "${GREEN}Android构建完成!${NC}"
echo -e "${YELLOW}输出文件: $OUTPUT_DIR/libimage_blend_ffi.so${NC}"
