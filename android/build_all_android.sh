#!/bin/bash

# Android多架构构建脚本 - 生成所有架构的.so文件
# 使用方法: ./build_all_android.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}开始构建Android多架构.so文件${NC}"

# 支持的架构列表
ARCHITECTURES=("arm64-v8a" "armeabi-v7a" "x86" "x86_64")
API_LEVEL="21"

# 检查环境变量
if [ -z "$ANDROID_NDK_ROOT" ] && [ -z "$ANDROID_NDK" ]; then
    echo -e "${RED}错误: 请设置ANDROID_NDK_ROOT或ANDROID_NDK环境变量${NC}"
    echo "例如: export ANDROID_NDK_ROOT=/path/to/android-ndk"
    exit 1
fi

if [ -z "$OPENCV_ANDROID_SDK" ]; then
    echo -e "${RED}错误: 请设置OPENCV_ANDROID_SDK环境变量${NC}"
    echo "例如: export OPENCV_ANDROID_SDK=/path/to/OpenCV-android-sdk"
    exit 1
fi

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${YELLOW}API级别: $API_LEVEL${NC}"

# 构建每个架构
for arch in "${ARCHITECTURES[@]}"; do
    echo -e "${GREEN}======================================${NC}"
    echo -e "${GREEN}构建架构: $arch${NC}"
    echo -e "${GREEN}======================================${NC}"
    
    # 调用单架构构建脚本
    if [ -f "$SCRIPT_DIR/build_android.sh" ]; then
        "$SCRIPT_DIR/build_android.sh" "$arch" "$API_LEVEL"
    else
        echo -e "${RED}错误: 未找到build_android.sh脚本${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}? $arch 构建完成${NC}"
    echo ""
done

# 显示构建结果
echo -e "${GREEN}======================================${NC}"
echo -e "${GREEN}所有架构构建完成!${NC}"
echo -e "${GREEN}======================================${NC}"

echo -e "${GREEN}构建结果:${NC}"
LIBS_DIR="$PROJECT_ROOT/android/libs"

if [ -d "$LIBS_DIR" ]; then
    for arch in "${ARCHITECTURES[@]}"; do
        SO_FILE="$LIBS_DIR/$arch/libimage_blend_ffi.so"
        if [ -f "$SO_FILE" ]; then
            echo -e "${GREEN}? $arch:${NC} $SO_FILE"
            echo -e "  大小: $(ls -lh "$SO_FILE" | awk '{print $5}')"
            echo -e "  架构: $(file "$SO_FILE" | cut -d: -f2)"
        else
            echo -e "${RED}? $arch:${NC} 构建失败"
        fi
    done
else
    echo -e "${RED}错误: 未找到libs目录${NC}"
fi

echo ""
echo -e "${YELLOW}使用说明:${NC}"
echo "1. 将生成的.so文件复制到Flutter项目的android/src/main/jniLibs/目录"
echo "2. 目录结构应该是:"
echo "   android/src/main/jniLibs/"
echo "   ├── arm64-v8a/libimage_blend_ffi.so"
echo "   ├── armeabi-v7a/libimage_blend_ffi.so"
echo "   ├── x86/libimage_blend_ffi.so"
echo "   └── x86_64/libimage_blend_ffi.so"
echo ""
echo -e "${GREEN}Android多架构构建完成!${NC}"
