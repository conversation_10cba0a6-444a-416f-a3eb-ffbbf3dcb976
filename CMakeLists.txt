cmake_minimum_required(VERSION 3.16)
project(image_blend_ffi)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Options
option(STATIC_OPENCV "Static link OpenCV" OFF)
option(IOS_FRAMEWORK "Build iOS Framework" OFF)

# Platform specific configuration
if(ANDROID)
    # Android configuration - static link OpenCV
    message(STATUS "Configuring Android build")

    if(DEFINED OPENCV_ANDROID_SDK)
        set(OpenCV_DIR "${OPENCV_ANDROID_SDK}/sdk/native/jni")
        message(STATUS "Using OpenCV Android SDK: ${OPENCV_ANDROID_SDK}")
    endif()

    find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs)
    include_directories(${OpenCV_INCLUDE_DIRS})

    # Force static linking
    set(STATIC_OPENCV ON)

elseif(IOS)
    # iOS configuration - static link OpenCV
    message(STATUS "Configuring iOS build")

    if(DEFINED OPENCV_IOS_SDK)
        set(OpenCV_DIR "${OPENCV_IOS_SDK}")
        message(STATUS "Using OpenCV iOS SDK: ${OPENCV_IOS_SDK}")

        # Manually set OpenCV paths
        set(OpenCV_INCLUDE_DIRS "${OPENCV_IOS_SDK}/Headers")
        set(OpenCV_LIBS "${OPENCV_IOS_SDK}/opencv2")
        include_directories(${OpenCV_INCLUDE_DIRS})
    else()
        find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs)
        include_directories(${OpenCV_INCLUDE_DIRS})
    endif()

    # Force static linking and Framework build
    set(STATIC_OPENCV ON)
    set(IOS_FRAMEWORK ON)

elseif(APPLE)
    # macOS configuration (for development testing)
    message(STATUS "Configuring macOS build")
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(OPENCV REQUIRED opencv4)
    include_directories(${OPENCV_INCLUDE_DIRS})
    include_directories("/opt/homebrew/include")
    link_directories("/opt/homebrew/lib")
    link_directories(${OPENCV_LIBRARY_DIRS})
    set(CMAKE_OSX_DEPLOYMENT_TARGET "11.0")

else()
    # Other platforms (Linux etc)
    message(STATUS "Configuring other platform build")
    find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs)
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

# ????????????? CTPL ??????
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${CMAKE_SOURCE_DIR}/extern/CTPL)

# ?????????????
if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /RTC1")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang" OR CMAKE_CXX_COMPILER_ID STREQUAL "AppleClang")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fexceptions -frtti")
endif()

# Create library target
if(IOS_FRAMEWORK)
    # iOS Framework
    add_library(image_blend_ffi SHARED
        src/image_blend_ffi/image_blend_ffi.cpp
    )

    # Set Framework properties
    set_target_properties(image_blend_ffi PROPERTIES
        FRAMEWORK TRUE
        MACOSX_FRAMEWORK_IDENTIFIER com.example.imageblend
        MACOSX_FRAMEWORK_BUNDLE_VERSION "1.0"
        MACOSX_FRAMEWORK_SHORT_VERSION_STRING "1.0"
        PUBLIC_HEADER "${CMAKE_SOURCE_DIR}/src/image_blend_ffi/image_blend_ffi.h"
        MACOSX_FRAMEWORK_INFO_PLIST "${CMAKE_SOURCE_DIR}/ios/Info.plist"
    )
else()
    # Shared library
    add_library(image_blend_ffi SHARED
        src/image_blend_ffi/image_blend_ffi.cpp
    )
endif()

# Link libraries
if(ANDROID)
    if(STATIC_OPENCV)
        # Android static link OpenCV
        target_link_libraries(image_blend_ffi
            ${OpenCV_LIBS}
            -static-libgcc
            -static-libstdc++
        )
    else()
        target_link_libraries(image_blend_ffi ${OpenCV_LIBS})
    endif()

elseif(IOS)
    if(STATIC_OPENCV AND DEFINED OPENCV_IOS_SDK)
        # iOS static link OpenCV
        target_link_libraries(image_blend_ffi
            "${OPENCV_IOS_SDK}/Versions/A/opencv2"
            "-framework Foundation"
            "-framework UIKit"
            "-framework CoreGraphics"
            "-framework CoreImage"
            "-framework CoreMedia"
            "-framework AVFoundation"
        )
    else()
        target_link_libraries(image_blend_ffi ${OpenCV_LIBS})
    endif()

elseif(APPLE)
    target_link_libraries(image_blend_ffi ${OPENCV_LIBRARIES})
else()
    target_link_libraries(image_blend_ffi ${OpenCV_LIBS})
endif()

# Set output directories
set_target_properties(image_blend_ffi PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    FRAMEWORK_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/Frameworks"
)

# Android specific configuration
if(ANDROID)
    set_target_properties(image_blend_ffi PROPERTIES
        ANDROID_ARM_NEON TRUE
    )

    # Set RPATH for runtime library finding
    set_target_properties(image_blend_ffi PROPERTIES
        INSTALL_RPATH_USE_LINK_PATH TRUE
    )
endif()

# Install configuration
if(IOS_FRAMEWORK)
    install(TARGETS image_blend_ffi
        FRAMEWORK DESTINATION Frameworks
    )
else()
    install(TARGETS image_blend_ffi
        LIBRARY DESTINATION lib
        RUNTIME DESTINATION bin
    )

    install(FILES src/image_blend_ffi/image_blend_ffi.h
        DESTINATION include
    )
endif()

# Display configuration info
message(STATUS "=== Build Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Static OpenCV: ${STATIC_OPENCV}")
message(STATUS "iOS Framework: ${IOS_FRAMEWORK}")
if(ANDROID)
    message(STATUS "Android ABI: ${ANDROID_ABI}")
    message(STATUS "Android API: ${ANDROID_PLATFORM}")
endif()
if(IOS)
    message(STATUS "iOS Platform: ${PLATFORM}")
    message(STATUS "iOS Deployment Target: ${CMAKE_OSX_DEPLOYMENT_TARGET}")
endif()
message(STATUS "==========================")
