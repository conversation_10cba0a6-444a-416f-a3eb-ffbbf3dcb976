/// ͼ���Ϸ��� - �߼���װ�ͱ�ݷ���
library image_blend_service;

import 'dart:io';
import 'package:flutter/foundation.dart';
import '../image_blend_ffi.dart';
import '../models/blend_models.dart';

/// ͼ���Ϸ�����
class ImageBlendService {
  static ImageBlendService? _instance;
  final ImageBlendFFI _ffi = ImageBlendFFI();

  /// ����ģʽ
  factory ImageBlendService() {
    return _instance ??= ImageBlendService._internal();
  }

  ImageBlendService._internal();

  /// ʹ�� compute ִ��ͼ����ӣ��Ƽ����� Flutter UI��
  Future<FFIResult> overlayImages({
    required String baseImagePath,
    required String topImagePath,
    required String outputPath,
    BlendMode blendMode = BlendMode.normal,
    TilingMode tilingMode = TilingMode.stretch,
    int opacity = 100,
    int tilingScale = 100,
    String? maskImagePath,
  }) async {
    final params = OverlayImageParams(
      baseImagePath: baseImagePath,
      topImagePath: topImagePath,
      outputPath: outputPath,
      blendMode: blendMode,
      tilingMode: tilingMode,
      opacity: opacity,
      tilingScale: tilingScale,
      maskImagePath: maskImagePath,
    );

    // ��֤�ļ��Ƿ����
    final validationResult = await _validateInputFiles([
      baseImagePath,
      topImagePath,
      if (maskImagePath != null) maskImagePath,
    ]);

    if (!validationResult.isSuccess) {
      return validationResult;
    }

    // ʹ�� compute �ں�̨�߳�ִ��
    return await compute(_overlayImagesCompute, params);
  }

  /// ʹ�� Isolate ִ��ͼ����ӣ������ڳ�ʱ�����е�����
  Future<FFIResult> overlayImagesWithIsolate({
    required String baseImagePath,
    required String topImagePath,
    required String outputPath,
    BlendMode blendMode = BlendMode.normal,
    TilingMode tilingMode = TilingMode.stretch,
    int opacity = 100,
    int tilingScale = 100,
    String? maskImagePath,
  }) async {
    final params = OverlayImageParams(
      baseImagePath: baseImagePath,
      topImagePath: topImagePath,
      outputPath: outputPath,
      blendMode: blendMode,
      tilingMode: tilingMode,
      opacity: opacity,
      tilingScale: tilingScale,
      maskImagePath: maskImagePath,
    );

    // ��֤�ļ��Ƿ����
    final validationResult = await _validateInputFiles([
      baseImagePath,
      topImagePath,
      if (maskImagePath != null) maskImagePath,
    ]);

    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.overlayImagesAsync(params);
  }

  /// ʹ�� compute ִ��ͼ��ƽ��
  Future<FFIResult> tileImage({
    required String imagePath,
    required String outputPath,
    int tileMultiplier = 2,
  }) async {
    final params = TileImageParams(
      imagePath: imagePath,
      outputPath: outputPath,
      tileMultiplier: tileMultiplier,
    );

    // ��֤�ļ��Ƿ����
    final validationResult = await _validateInputFiles([imagePath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await compute(_tileImageCompute, params);
  }

  /// ʹ�� Isolate ִ��ͼ��ƽ��
  Future<FFIResult> tileImageWithIsolate({
    required String imagePath,
    required String outputPath,
    int tileMultiplier = 2,
  }) async {
    final params = TileImageParams(
      imagePath: imagePath,
      outputPath: outputPath,
      tileMultiplier: tileMultiplier,
    );

    // ��֤�ļ��Ƿ����
    final validationResult = await _validateInputFiles([imagePath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.tileImageAsync(params);
  }

  /// ʹ�� compute ִ��ͼ��ü�
  Future<FFIResult> trimImage({
    required String inputPath,
    required String outputPath,
  }) async {
    final params = TrimImageParams(
      inputPath: inputPath,
      outputPath: outputPath,
    );

    // ��֤�ļ��Ƿ����
    final validationResult = await _validateInputFiles([inputPath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await compute(_trimImageCompute, params);
  }

  /// ʹ�� Isolate ִ��ͼ��ü�
  Future<FFIResult> trimImageWithIsolate({
    required String inputPath,
    required String outputPath,
  }) async {
    final params = TrimImageParams(
      inputPath: inputPath,
      outputPath: outputPath,
    );

    // ��֤�ļ��Ƿ����
    final validationResult = await _validateInputFiles([inputPath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.trimImageAsync(params);
  }

  /// ��������
  Future<FFIResult> clearCache() async {
    return await compute(_clearCacheCompute, null);
  }

  /// ʹ�� Isolate ��������
  Future<FFIResult> clearCacheWithIsolate() async {
    return await ImageBlendAsyncHelper.clearCacheAsync();
  }

  /// ��������ͼ�����
  Future<List<FFIResult>> batchOverlayImages(
    List<OverlayImageParams> paramsList, {
    bool useIsolate = false,
  }) async {
    final results = <FFIResult>[];

    for (final params in paramsList) {
      FFIResult result;
      if (useIsolate) {
        result = await ImageBlendAsyncHelper.overlayImagesAsync(params);
      } else {
        result = await compute(_overlayImagesCompute, params);
      }
      results.add(result);
    }

    return results;
  }

  /// ��֤�����ļ��Ƿ����
  Future<FFIResult> _validateInputFiles(List<String> filePaths) async {
    for (final path in filePaths) {
      final file = File(path);
      if (!await file.exists()) {
        return FFIResult.error(FFIErrorCodes.fileNotFound, '�ļ�������: $path');
      }
    }
    return FFIResult.success();
  }

  /// ������Ŀ¼�Ƿ���ڣ��������򴴽�
  Future<void> _ensureOutputDirectory(String outputPath) async {
    final file = File(outputPath);
    final directory = file.parent;
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }
}

/// Compute �����������Ƕ���������

/// �� compute ��ִ��ͼ�����
FFIResult _overlayImagesCompute(OverlayImageParams params) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.overlayImagesSync(params);
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, 'ͼ�����ʧ��: $e');
  }
}

/// �� compute ��ִ��ͼ��ƽ��
FFIResult _tileImageCompute(TileImageParams params) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.tileImageSync(params);
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, 'ͼ��ƽ��ʧ��: $e');
  }
}

/// �� compute ��ִ��ͼ��ü�
FFIResult _trimImageCompute(TrimImageParams params) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.trimImageSync(params);
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, 'ͼ��ü�ʧ��: $e');
  }
}

/// �� compute ��ִ����������
FFIResult _clearCacheCompute(void _) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.clearCacheSync();
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, '��������ʧ��: $e');
  }
}
